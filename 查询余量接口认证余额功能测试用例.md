## 冒烟测试

### MYTL-orderType传入AUTH枚举查询认证余额基本验证

#### PD-前置条件：接口服务正常运行；用户具有查询权限；

#### 步骤一：调用查询余量接口

#### 步骤二：设置orderType参数为AUTH

#### 步骤三：发送请求并获取响应

#### ER-预期结果：1：接口调用成功；2：返回认证余额信息；3：数据格式正确；

### MYTL-客户查询认证余额核心流程验证

#### PD-前置条件：客户账户存在；具有认证余额；

#### 步骤一：客户调用查询余量接口

#### 步骤二：传入orderType为AUTH

#### 步骤三：获取认证余额信息

#### ER-预期结果：1：获取到准确的认证余额；2：可用于充值决策；3：流程无异常；

### MYTL-orderType参数异常值处理验证

#### PD-前置条件：接口服务正常运行；

#### 步骤一：调用查询余量接口

#### 步骤二：设置orderType为无效值

#### 步骤三：检查错误处理

#### ER-预期结果：1：返回参数错误；2：错误信息清晰；3：系统保持稳定；