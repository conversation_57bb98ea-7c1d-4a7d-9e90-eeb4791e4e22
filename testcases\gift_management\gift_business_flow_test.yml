- config:
    name: 赠送管理业务流程测试
    base_url: ${ENV(base_url)}
    variables:
        - token: ${ENV(auth_token)}
        - customer_id: ${ENV(test_customer_id)}

- test:
    name: 产品试用完整流程-规则检查到赠送申请
    variables:
        - subType: PRODUCT_TRIAL
    api: api/base-billing-manager/approval/checkrule.yml
    extract:
        - allowed_commodities: content.data.0.ruleValue.commodityIds
        - max_effect_days: content.data.1.ruleValue.maxEffectDays
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["$allowed_commodities", "list"]
        - eq: ["$max_effect_days", 15]

- test:
    name: 营销赠送完整流程-规则检查到赠送申请
    variables:
        - subType: MARKETING_GIFT
    api: api/base-billing-manager/approval/checkrule.yml
    extract:
        - marketing_rules: content.data
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["$marketing_rules", "list"]

- test:
    name: 销售权限验证-名下客户赠送权限
    variables:
        - subType: PRODUCT_TRIAL
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["content.data", "list"]