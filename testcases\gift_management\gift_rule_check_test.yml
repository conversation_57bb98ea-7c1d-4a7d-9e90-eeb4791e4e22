- config:
    name: 赠送管理规则检查接口测试
    base_url: ${ENV(base_url)}
    variables:
        - token: ${ENV(auth_token)}

- test:
    name: 产品试用场景规则查询-正常流程
    variables:
        - subType: PRODUCT_TRIAL
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["content.data", "list"]
        - contains: ["content.data.0.ruleCode", "COMMIDITY_SCOPE"]
        - contains: ["content.data.0.ruleValue.commodityIds", 690]
        - contains: ["content.data.0.ruleValue.commodityIds", 475]
        - contains: ["content.data.0.ruleValue.commodityIds", 191]
        - contains: ["content.data.0.ruleValue.commodityIds", 719]

- test:
    name: 营销赠送场景规则查询-正常流程
    variables:
        - subType: MARKETING_GIFT
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["content.data", "list"]
        - contains: ["content.data", "EFFECT_DATE_RANGE"]

- test:
    name: 提前交付场景规则查询-正常流程
    variables:
        - subType: ADVANCE_DELIVERY
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["content.data", "list"]

- test:
    name: 产品试用场景-验证商品范围规则
    variables:
        - subType: PRODUCT_TRIAL
    api: api/base-billing-manager/approval/checkrule.yml
    extract:
        - commodity_rule: content.data.0.ruleValue.commodityIds
    validate:
        - eq: ["status_code", 200]
        - contains: ["$commodity_rule", 690]  # e签宝基础版
        - contains: ["$commodity_rule", 475]  # e签宝专业版
        - contains: ["$commodity_rule", 191]  # e签宝高级版
        - contains: ["$commodity_rule", 719]  # e签宝旗舰版
        - contains: ["$commodity_rule", 630]  # AI算力套餐包
        - contains: ["$commodity_rule", 48]   # 电子签名流量（组合）
        - contains: ["$commodity_rule", 443]  # 电子签名流量（分项）签署
        - contains: ["$commodity_rule", 444]  # 电子签名流量（分项）认证

- test:
    name: 产品试用场景-验证有效期规则
    variables:
        - subType: PRODUCT_TRIAL
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.data.1.ruleCode", "EFFECT_DATE_RANGE"]
        - eq: ["content.data.1.ruleValue.maxEffectDays", 15]

- test:
    name: 产品试用场景-验证续费禁用规则
    variables:
        - subType: PRODUCT_TRIAL
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 200]
        - contains: ["content.data", "DISABLE_RENEWAL"]
        - eq: ["content.data.2.ruleValue.disableRenewal", true]

- test:
    name: 产品试用场景-验证年度试用次数限制
    variables:
        - subType: PRODUCT_TRIAL
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 200]
        - contains: ["content.data", "RANGE_TIMES_VALID"]
        - eq: ["content.data.3.ruleValue.rangeType", "YEAR"]
        - eq: ["content.data.3.ruleValue.rangeRule.maxCount", 1]

- test:
    name: 产品试用场景-验证成本限制规则
    variables:
        - subType: PRODUCT_TRIAL
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 200]
        - contains: ["content.data", "RANGE_AMOUNT_VALID"]
        - eq: ["content.data.4.ruleValue.rangeType", "YEAR"]
        - eq: ["content.data.4.ruleValue.rangeRule.maxValidAmount", 100]

- test:
    name: 无效赠送场景类型-异常处理
    variables:
        - subType: INVALID_TYPE
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "无效的赠送场景类型"]

- test:
    name: 空赠送场景类型-异常处理
    variables:
        - subType: ""
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]

- test:
    name: 未授权访问-权限验证
    variables:
        - subType: PRODUCT_TRIAL
        - token: "invalid_token"
    api: api/base-billing-manager/approval/checkrule.yml
    validate:
        - eq: ["status_code", 401]
        - eq: ["content.success", false]
        - contains: ["content.message", "未授权"]