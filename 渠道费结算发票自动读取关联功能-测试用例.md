# 【计费】渠道费结算自动读取、关联税信系统发票信息-测试用例

## 功能测试

### 发票自动同步功能

#### TL-发票自动同步-正常同步流程验证
##### PD-前置条件：乐企平台有新增发票数据；系统定时任务正常运行；数据库连接正常
##### 步骤一：等待系统每日1点自动触发发票同步任务
##### 步骤二：查看系统日志确认同步任务执行
##### 步骤三：查询sc_receive_invoice表验证发票数据是否正确入库
##### 步骤四：验证发票基本信息字段映射是否正确
##### ER-预期结果
###### 1：定时任务在每日1点准时触发执行
###### 2：系统日志显示同步任务执行成功
###### 3：发票数据成功入库，字段映射正确
###### 4：发票号、开票日期、税率、金额等关键信息准确无误

#### TL-发票自动同步-接口调用失败处理
##### PD-前置条件：乐企平台接口异常或网络不通；定时任务正常配置
##### 步骤一：模拟乐企平台接口返回异常或超时
##### 步骤二：触发发票同步任务执行
##### 步骤三：查看系统错误日志记录
##### 步骤四：验证系统是否有重试机制
##### ER-预期结果
###### 1：系统能正确捕获接口调用异常
###### 2：错误日志详细记录失败原因和时间
###### 3：系统不会因接口失败而崩溃
###### 4：后续同步任务能正常恢复执行

#### TL-发票自动同步-数据解析异常处理
##### PD-前置条件：乐企平台返回数据格式异常；系统正常运行
##### 步骤一：模拟乐企平台返回格式错误的发票数据
##### 步骤二：执行发票同步任务
##### 步骤三：查看数据解析错误日志
##### 步骤四：验证异常数据是否被正确过滤
##### ER-预期结果
###### 1：系统能识别并过滤异常格式数据
###### 2：错误日志记录具体的解析失败信息
###### 3：正常格式的发票数据仍能正确处理
###### 4：系统整体功能不受异常数据影响

#### TL-发票自动同步-重复数据处理
##### PD-前置条件：数据库中已存在相同发票号的发票记录；乐企平台返回重复数据
##### 步骤一：在数据库中插入测试发票数据
##### 步骤二：模拟乐企平台返回相同发票号的数据
##### 步骤三：执行发票同步任务
##### 步骤四：查询数据库验证重复数据处理结果
##### ER-预期结果
###### 1：系统能识别重复的发票号
###### 2：重复数据不会重复入库
###### 3：原有发票数据保持不变
###### 4：同步任务正常完成，无异常报错

### 发票关联管理功能

#### TL-发票关联-正常关联流程验证
##### PD-前置条件：系统中存在未关联的进项发票；存在待关联的渠道账单；用户具有关联发票权限
##### 步骤一：销售用户登录系统，进入渠道对账管理页面
##### 步骤二：选择一个待关联发票的账单，点击详情进入详情页
##### 步骤三：点击【关联渠道发票】按钮，弹出发票选择弹窗
##### 步骤四：在弹窗中选择合适的发票，点击确认关联
##### 步骤五：返回账单详情页，查看发票关联结果
##### ER-预期结果
###### 1：弹窗正确展示该渠道商的可关联发票列表
###### 2：发票信息显示完整（开票日期、发票号、税率、金额等）
###### 3：发票关联成功，账单详情页显示已关联发票
###### 4：关联的发票状态更新为已使用，不再出现在可关联列表中

#### TL-发票关联-税率一致性校验
##### PD-前置条件：账单中已关联税率为13%的发票；系统中存在税率为6%的可关联发票
##### 步骤一：进入已有13%税率发票的账单详情页
##### 步骤二：点击【关联渠道发票】，打开发票选择弹窗
##### 步骤三：尝试勾选税率为6%的发票
##### 步骤四：点击确认关联按钮
##### ER-预期结果
###### 1：税率为6%的发票在列表中显示为不可勾选状态
###### 2：鼠标悬停时显示提示信息"发票税率需一致，请重新勾选"
###### 3：确认按钮在选择不同税率发票时保持禁用状态
###### 4：系统阻止不同税率发票的关联操作

#### TL-发票关联-单发票默认勾选
##### PD-前置条件：某渠道商仅有一张可关联的发票；账单未关联任何发票
##### 步骤一：进入该渠道商的账单详情页
##### 步骤二：点击【关联渠道发票】按钮
##### 步骤三：查看弹窗中发票的勾选状态
##### 步骤四：直接点击确认关联
##### ER-预期结果
###### 1：弹窗打开时，唯一的发票自动处于勾选状态
###### 2：确认按钮处于可用状态
###### 3：可以直接完成关联操作
###### 4：关联成功后发票状态正确更新

#### TL-发票关联-权限控制验证
##### PD-前置条件：存在无关联发票权限的用户账号；系统中有可关联的发票和账单
##### 步骤一：使用无权限用户登录系统
##### 步骤二：进入渠道对账管理页面
##### 步骤三：选择账单进入详情页
##### 步骤四：查看页面中是否显示【关联渠道发票】按钮
##### ER-预期结果
###### 1：无权限用户无法看到【关联渠道发票】按钮
###### 2：即使通过直接访问URL也无法进入发票关联页面
###### 3：系统返回权限不足的提示信息
###### 4：用户只能查看已关联的发票信息，无法进行关联操作

### 发票列表刷新功能

#### TL-发票刷新-手动刷新成功
##### PD-前置条件：发票关联弹窗已打开；乐企平台有新增发票数据；网络连接正常
##### 步骤一：在发票关联弹窗中点击【刷新】按钮
##### 步骤二：观察刷新过程中的界面状态
##### 步骤三：等待刷新完成，查看发票列表变化
##### 步骤四：验证新增发票是否出现在列表中
##### ER-预期结果
###### 1：点击刷新后按钮变为加载状态，列表暂时不可操作
###### 2：系统调用乐企接口获取最新发票数据
###### 3：刷新完成后列表更新，显示最新的可关联发票
###### 4：新增的发票按开票日期倒序排列在列表中

#### TL-发票刷新-刷新失败处理
##### PD-前置条件：发票关联弹窗已打开；乐企平台接口异常或网络中断
##### 步骤一：模拟网络异常或接口故障
##### 步骤二：在发票关联弹窗中点击【刷新】按钮
##### 步骤三：等待刷新操作超时或失败
##### 步骤四：查看系统的错误提示信息
##### ER-预期结果
###### 1：刷新失败后系统显示明确的错误提示
###### 2：错误信息包含具体的失败原因（如"接口异常，请联系管理员"）
###### 3：列表恢复可操作状态，显示刷新前的数据
###### 4：用户可以重新尝试刷新操作

### 发票文件管理功能

#### TL-发票文件-PDF文件查看
##### PD-前置条件：发票记录中包含有效的PDF文件链接；用户有查看发票权限
##### 步骤一：在发票列表中找到包含PDF文件的发票记录
##### 步骤二：点击发票记录中的【查看】链接
##### 步骤三：验证PDF文件是否能正常打开
##### 步骤四：检查PDF文件内容是否与发票信息一致
##### ER-预期结果
###### 1：点击查看链接后能成功打开PDF文件
###### 2：PDF文件内容清晰可读，格式正确
###### 3：PDF中的发票信息与系统记录的信息一致
###### 4：文件加载速度合理，用户体验良好

#### TL-发票文件-文件链接失效处理
##### PD-前置条件：发票记录中的文件链接已失效或文件已删除
##### 步骤一：点击失效链接的发票文件查看按钮
##### 步骤二：观察系统的响应和错误处理
##### 步骤三：查看是否有友好的错误提示
##### ER-预期结果
###### 1：系统能检测到文件链接失效
###### 2：显示友好的错误提示信息，如"文件暂时无法访问"
###### 3：不会出现系统错误或页面崩溃
###### 4：用户可以继续进行其他操作

## 冒烟测试

### MYTL-发票同步基本功能验证
##### PD-前置条件：系统正常运行；乐企平台接口可用
##### 步骤一：手动触发发票同步任务
##### 步骤二：查看同步结果和日志
##### ER-预期结果
###### 1：同步任务正常执行完成
###### 2：发票数据成功入库

### MYTL-发票关联核心流程验证
##### PD-前置条件：存在可关联发票和账单；用户有相应权限
##### 步骤一：进入账单详情页，点击关联发票
##### 步骤二：选择发票并确认关联
##### ER-预期结果
###### 1：发票关联弹窗正常打开
###### 2：发票关联成功，状态正确更新

### MYTL-权限控制基本验证
##### PD-前置条件：存在有权限和无权限的用户账号
##### 步骤一：分别使用不同权限用户登录
##### 步骤二：访问发票关联功能
##### ER-预期结果
###### 1：有权限用户可正常使用功能
###### 2：无权限用户被正确拦截

## 线上验证测试

### PATL-生产环境发票同步验证
##### PD-前置条件：生产环境系统正常；乐企生产接口可用
##### 步骤一：确认定时任务在生产环境正常运行
##### 步骤二：验证发票数据同步的准确性和完整性
##### ER-预期结果
###### 1：定时任务按计划执行
###### 2：发票数据同步准确无误

### PATL-关键业务流程端到端验证
##### PD-前置条件：生产环境有真实的渠道商和账单数据
##### 步骤一：使用真实账单进行发票关联操作
##### 步骤二：验证整个业务流程的完整性
##### ER-预期结果
###### 1：发票关联功能在生产环境正常工作
###### 2：业务流程完整，数据一致性良好

### PATL-系统性能和稳定性验证
##### PD-前置条件：生产环境正常运行一段时间
##### 步骤一：监控系统性能指标
##### 步骤二：检查错误日志和异常情况
##### ER-预期结果
###### 1：系统性能稳定，响应时间合理
###### 2：无严重错误和异常，系统运行稳定

## 异常流程测试

### 数据异常处理

#### TL-数据异常-发票金额为零处理
##### PD-前置条件：乐企平台返回金额为0的发票数据
##### 步骤一：模拟乐企接口返回金额为0的发票
##### 步骤二：执行发票同步任务
##### 步骤三：查看系统对零金额发票的处理方式
##### 步骤四：验证前端是否展示零金额发票
##### ER-预期结果
###### 1：系统能正确处理零金额发票数据
###### 2：零金额发票正常入库，不被过滤
###### 3：前端列表中正确显示零金额发票
###### 4：零金额发票可以正常参与关联操作

#### TL-数据异常-发票日期格式异常
##### PD-前置条件：乐企平台返回日期格式不标准的发票数据
##### 步骤一：模拟返回日期格式异常的发票数据
##### 步骤二：执行发票同步任务
##### 步骤三：查看日期解析错误的处理逻辑
##### 步骤四：验证异常数据是否影响正常数据处理
##### ER-预期结果
###### 1：系统能识别日期格式异常
###### 2：异常数据被记录到错误日志中
###### 3：异常数据不会导致整个同步任务失败
###### 4：正常格式的发票数据继续正常处理

#### TL-数据异常-必填字段缺失处理
##### PD-前置条件：乐企平台返回缺少发票号或税号等必填字段的数据
##### 步骤一：模拟返回缺少必填字段的发票数据
##### 步骤二：执行发票同步任务
##### 步骤三：查看必填字段校验逻辑
##### 步骤四：验证缺失数据的过滤机制
##### ER-预期结果
###### 1：系统能检测到必填字段缺失
###### 2：缺失必填字段的数据被过滤，不入库
###### 3：错误日志详细记录缺失字段信息
###### 4：完整数据正常入库，不受影响

### 并发操作测试

#### TL-并发操作-多用户同时关联发票
##### PD-前置条件：多个用户同时登录系统；存在可关联的发票
##### 步骤一：用户A进入发票关联页面，选择发票但未确认
##### 步骤二：用户B同时进入相同发票关联页面
##### 步骤三：用户A先确认关联操作
##### 步骤四：用户B再尝试关联相同发票
##### ER-预期结果
###### 1：用户A成功关联发票
###### 2：用户B的页面实时更新，已关联发票从列表中移除
###### 3：用户B无法关联已被使用的发票
###### 4：系统提示发票已被其他用户关联

#### TL-并发操作-同步任务与手动刷新冲突
##### PD-前置条件：定时同步任务即将执行；用户正在进行手动刷新
##### 步骤一：在定时任务执行前几秒，用户点击手动刷新
##### 步骤二：定时任务开始执行
##### 步骤三：观察两个操作的执行情况
##### 步骤四：验证数据一致性和操作结果
##### ER-预期结果
###### 1：系统能正确处理并发的同步操作
###### 2：不会出现数据重复或丢失
###### 3：用户操作得到正确响应
###### 4：最终数据状态保持一致

### 边界条件测试

#### TL-边界条件-大量发票数据处理
##### PD-前置条件：乐企平台返回超过1000条发票记录
##### 步骤一：模拟乐企接口返回大量发票数据
##### 步骤二：执行发票同步任务
##### 步骤三：监控系统内存和CPU使用情况
##### 步骤四：验证大量数据的处理效率和准确性
##### ER-预期结果
###### 1：系统能稳定处理大量发票数据
###### 2：内存使用控制在合理范围内
###### 3：所有发票数据准确入库
###### 4：处理时间在可接受范围内

#### TL-边界条件-发票号长度极限测试
##### PD-前置条件：存在超长发票号的测试数据
##### 步骤一：创建发票号长度接近数据库字段限制的测试数据
##### 步骤二：执行发票同步操作
##### 步骤三：验证超长发票号的处理方式
##### 步骤四：检查数据库存储结果
##### ER-预期结果
###### 1：系统能正确处理长发票号
###### 2：超出长度限制的发票号被适当截断或拒绝
###### 3：不会导致数据库错误或系统崩溃
###### 4：错误情况有明确的日志记录

#### TL-边界条件-税率精度处理
##### PD-前置条件：存在税率精度超过4位小数的发票数据
##### 步骤一：模拟税率为0.12345的发票数据
##### 步骤二：执行发票同步任务
##### 步骤三：查看税率精度的处理方式
##### 步骤四：验证前端显示和计算的准确性
##### ER-预期结果
###### 1：系统按照业务规则处理税率精度
###### 2：税率精度统一保留到4位小数
###### 3：前端显示格式一致
###### 4：税率计算结果准确无误

## 接口测试

### 发票查询接口测试

#### TL-接口测试-分页查询参数验证
##### PD-前置条件：系统中存在多条发票记录；接口服务正常运行
##### 步骤一：调用发票分页查询接口，传入正常分页参数
##### 步骤二：验证返回结果的分页信息准确性
##### 步骤三：测试边界分页参数（如页码为0、负数等）
##### 步骤四：验证异常参数的处理方式
##### ER-预期结果
###### 1：正常分页参数返回正确的分页数据
###### 2：分页信息包含总记录数、当前页码、总页数等
###### 3：异常分页参数返回合适的错误信息
###### 4：接口响应时间在合理范围内

#### TL-接口测试-查询条件过滤验证
##### PD-前置条件：系统中存在不同渠道商的发票数据
##### 步骤一：使用特定账单号调用发票查询接口
##### 步骤二：验证返回结果是否只包含该账单可关联的发票
##### 步骤三：测试不存在的账单号查询
##### 步骤四：验证查询结果的准确性和完整性
##### ER-预期结果
###### 1：查询结果准确过滤，只返回相关发票
###### 2：不存在的账单号返回空结果集
###### 3：查询条件生效，数据过滤正确
###### 4：返回数据格式符合接口规范

### 发票关联接口测试

#### TL-接口测试-关联请求参数校验
##### PD-前置条件：存在有效的账单号和发票号
##### 步骤一：使用正确参数调用发票关联接口
##### 步骤二：测试缺少必填参数的请求
##### 步骤三：测试参数格式错误的请求
##### 步骤四：验证参数校验的完整性
##### ER-预期结果
###### 1：正确参数的请求成功执行关联操作
###### 2：缺少必填参数返回明确的错误信息
###### 3：参数格式错误返回格式校验失败提示
###### 4：所有参数校验规则生效

#### TL-接口测试-关联业务逻辑验证
##### PD-前置条件：存在已关联和未关联的发票数据
##### 步骤一：尝试关联已被其他账单使用的发票
##### 步骤二：尝试关联税率不一致的发票
##### 步骤三：验证业务规则校验的有效性
##### 步骤四：测试正常关联操作的成功场景
##### ER-预期结果
###### 1：已关联发票无法重复关联，返回相应错误
###### 2：税率不一致的关联被阻止，提示税率冲突
###### 3：业务规则校验准确有效
###### 4：符合条件的关联操作成功执行

## 性能测试

### 数据同步性能测试

#### TL-性能测试-大批量发票同步性能
##### PD-前置条件：准备10000条发票测试数据；系统资源充足
##### 步骤一：模拟乐企接口返回10000条发票数据
##### 步骤二：执行发票同步任务，记录开始时间
##### 步骤三：监控同步过程中的系统资源使用情况
##### 步骤四：记录同步完成时间，计算处理效率
##### ER-预期结果
###### 1：10000条发票数据在30分钟内完成同步
###### 2：系统CPU使用率不超过80%
###### 3：内存使用稳定，无内存泄漏
###### 4：数据库连接池使用正常，无连接泄漏

#### TL-性能测试-并发查询性能验证
##### PD-前置条件：系统中存在大量发票数据；准备并发测试工具
##### 步骤一：模拟50个用户同时查询发票列表
##### 步骤二：记录每个查询请求的响应时间
##### 步骤三：监控数据库连接和系统资源使用
##### 步骤四：分析并发性能指标和系统稳定性
##### ER-预期结果
###### 1：95%的查询请求在3秒内返回结果
###### 2：系统在并发压力下保持稳定运行
###### 3：数据库查询性能良好，无慢查询
###### 4：用户体验良好，界面响应及时

### 接口响应性能测试

#### TL-性能测试-发票关联操作响应时间
##### PD-前置条件：系统正常运行；存在可关联的发票和账单
##### 步骤一：执行100次发票关联操作
##### 步骤二：记录每次操作的响应时间
##### 步骤三：计算平均响应时间和最大响应时间
##### 步骤四：分析响应时间分布和性能瓶颈
##### ER-预期结果
###### 1：发票关联操作平均响应时间不超过2秒
###### 2：95%的操作在3秒内完成
###### 3：最大响应时间不超过5秒
###### 4：操作成功率达到100%
