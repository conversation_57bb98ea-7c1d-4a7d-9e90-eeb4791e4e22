# 短信服务扣费来源区分功能-测试用例

## 功能测试

### 签署通知场景

#### TL-签署通知短信发送场景标识验证

##### PD-前置条件：签署流程已创建；短信服务正常；计费系统正常；

##### 步骤一：触发签署通知短信发送

##### 步骤二：系统自动标识短信来源为"签署通知"

##### 步骤三：记录扣费信息并关联场景标识

##### 步骤四：查看扣费明细

##### ER-预期结果：

###### 1：短信发送成功

###### 2：扣费记录中场景标识为"签署通知"

###### 3：扣费明细页面正确显示来源场景

###### 4：统计数据准确记录

#### TL-签署通知场景扣费金额计算验证

##### PD-前置条件：签署通知短信配置正确；计费规则已设置；

##### 步骤一：发送签署通知短信

##### 步骤二：系统按签署通知场景计费

##### 步骤三：生成扣费记录

##### 步骤四：验证扣费金额准确性

##### ER-预期结果：

###### 1：扣费金额按签署通知场景标准计算

###### 2：扣费记录包含正确的场景标识

###### 3：总费用统计准确

###### 4：客户账单显示明细正确

#### TL-签署通知批量短信场景标识一致性验证

##### PD-前置条件：存在多个签署流程；批量短信功能正常；

##### 步骤一：同时触发多个签署通知短信

##### 步骤二：系统批量处理短信发送

##### 步骤三：检查所有短信的场景标识

##### 步骤四：验证扣费记录一致性

##### ER-预期结果：

###### 1：所有签署通知短信场景标识一致

###### 2：批量扣费记录准确

###### 3：无场景标识错误或遗漏

###### 4：统计数据完整准确

### 实名认证场景

#### TL-实名认证短信发送场景标识验证

##### PD-前置条件：实名认证流程已启动；短信验证码功能正常；

##### 步骤一：用户申请实名认证短信验证码

##### 步骤二：系统发送验证码短信

##### 步骤三：系统标识短信来源为"实名认证"

##### 步骤四：记录扣费信息

##### ER-预期结果：

###### 1：验证码短信发送成功

###### 2：场景标识为"实名认证"

###### 3：扣费记录正确关联场景

###### 4：客户可查看明细来源

#### TL-实名认证重发短信场景标识保持验证

##### PD-前置条件：用户已申请过实名认证短信；重发功能正常；

##### 步骤一：用户点击重新发送验证码

##### 步骤二：系统重新发送短信

##### 步骤三：检查重发短信的场景标识

##### 步骤四：验证扣费记录

##### ER-预期结果：

###### 1：重发短信成功

###### 2：场景标识仍为"实名认证"

###### 3：产生新的扣费记录

###### 4：两次扣费记录场景标识一致

#### TL-实名认证多用户并发场景标识准确性验证

##### PD-前置条件：多个用户同时进行实名认证；系统支持并发；

##### 步骤一：多用户同时申请实名认证短信

##### 步骤二：系统并发处理短信发送

##### 步骤三：检查每个用户短信的场景标识

##### 步骤四：验证扣费记录准确性

##### ER-预期结果：

###### 1：所有用户短信发送成功

###### 2：每条短信场景标识均为"实名认证"

###### 3：扣费记录与用户一一对应

###### 4：无场景标识混乱情况

### 核身认证场景

#### TL-核身认证短信发送场景标识验证

##### PD-前置条件：核身认证流程已启动；短信服务配置正确；

##### 步骤一：触发核身认证短信发送

##### 步骤二：系统发送核身认证短信

##### 步骤三：系统标识短信来源为"核身认证"

##### 步骤四：生成扣费记录

##### ER-预期结果：

###### 1：核身认证短信发送成功

###### 2：场景标识准确为"核身认证"

###### 3：扣费记录包含正确场景信息

###### 4：统计数据实时更新

#### TL-核身认证失败重试场景标识一致性验证

##### PD-前置条件：核身认证首次失败；支持重试机制；

##### 步骤一：核身认证失败触发重试

##### 步骤二：系统重新发送核身认证短信

##### 步骤三：检查重试短信场景标识

##### 步骤四：对比前后扣费记录

##### ER-预期结果：

###### 1：重试短信发送成功

###### 2：场景标识保持"核身认证"

###### 3：产生独立的扣费记录

###### 4：场景标识前后一致

#### TL-核身认证跨时段场景统计准确性验证

##### PD-前置条件：核身认证在不同时段进行；统计功能正常；

##### 步骤一：在不同时段发送核身认证短信

##### 步骤二：系统记录每次发送的场景标识

##### 步骤三：查询跨时段统计数据

##### 步骤四：验证统计结果准确性

##### ER-预期结果：

###### 1：各时段短信发送成功

###### 2：场景标识均为"核身认证"

###### 3：跨时段统计数据准确

###### 4：时间维度统计正确

### 意愿认证场景

#### TL-意愿认证短信发送场景标识验证

##### PD-前置条件：意愿认证流程已启动；短信模板配置正确；

##### 步骤一：触发意愿认证短信发送

##### 步骤二：系统发送意愿认证短信

##### 步骤三：系统标识短信来源为"意愿认证"

##### 步骤四：记录扣费和统计信息

##### ER-预期结果：

###### 1：意愿认证短信发送成功

###### 2：场景标识准确为"意愿认证"

###### 3：扣费记录正确生成

###### 4：客户账单显示准确

#### TL-意愿认证确认短信场景标识验证

##### PD-前置条件：用户已完成意愿认证；确认短信功能正常；

##### 步骤一：用户完成意愿认证操作

##### 步骤二：系统发送确认短信

##### 步骤三：检查确认短信场景标识

##### 步骤四：验证扣费记录

##### ER-预期结果：

###### 1：确认短信发送成功

###### 2：场景标识为"意愿认证"

###### 3：扣费记录包含场景信息

###### 4：与认证短信场景标识一致

#### TL-意愿认证取消后短信场景标识验证

##### PD-前置条件：意愿认证流程可取消；取消通知功能正常；

##### 步骤一：用户取消意愿认证

##### 步骤二：系统发送取消通知短信

##### 步骤三：检查取消通知短信场景标识

##### 步骤四：验证扣费记录分类

##### ER-预期结果：

###### 1：取消通知短信发送成功

###### 2：场景标识仍为"意愿认证"

###### 3：扣费记录正确分类

###### 4：统计数据包含取消通知

### 其他场景

#### TL-未知场景短信自动归类验证

##### PD-前置条件：存在未预定义的短信发送场景；自动归类功能正常；

##### 步骤一：触发未知场景的短信发送

##### 步骤二：系统检测到未知场景

##### 步骤三：系统自动标识为"其他"场景

##### 步骤四：记录扣费信息

##### ER-预期结果：

###### 1：未知场景短信发送成功

###### 2：场景标识自动设置为"其他"

###### 3：扣费记录正确生成

###### 4：不影响其他场景统计

#### TL-系统维护短信场景标识验证

##### PD-前置条件：系统维护通知功能正常；短信服务可用；

##### 步骤一：触发系统维护通知短信

##### 步骤二：系统发送维护通知短信

##### 步骤三：检查维护通知场景标识

##### 步骤四：验证扣费记录

##### ER-预期结果：

###### 1：维护通知短信发送成功

###### 2：场景标识为"其他"

###### 3：扣费记录包含场景信息

###### 4：统计数据正确归类

#### TL-营销短信场景标识验证

##### PD-前置条件：营销短信功能已开启；短信内容合规；

##### 步骤一：发送营销推广短信

##### 步骤二：系统处理营销短信发送

##### 步骤三：检查营销短信场景标识

##### 步骤四：查看扣费明细

##### ER-预期结果：

###### 1：营销短信发送成功

###### 2：场景标识为"其他"

###### 3：扣费记录正确分类

###### 4：与业务短信区分明确

## 边界测试

### 边界值处理

#### TL-场景类型边界值验证

##### PD-前置条件：系统支持5种预定义场景类型；边界检测功能正常；

##### 步骤一：输入场景类型边界值

##### 步骤二：系统验证场景类型有效性

##### 步骤三：处理边界值情况

##### 步骤四：检查处理结果

##### ER-预期结果：

###### 1：有效场景类型正常处理

###### 2：无效场景类型自动归类为"其他"

###### 3：边界值处理逻辑正确

###### 4：不影响系统稳定性

#### TL-大量短信并发场景标识准确性验证

##### PD-前置条件：系统支持高并发；短信服务性能充足；

##### 步骤一：同时发送大量不同场景短信

##### 步骤二：系统并发处理短信发送

##### 步骤三：检查每条短信场景标识准确性

##### 步骤四：验证扣费记录完整性

##### ER-预期结果：

###### 1：大量短信发送成功

###### 2：每条短信场景标识准确

###### 3：扣费记录完整无遗漏

###### 4：系统性能稳定

#### TL-跨月统计数据准确性验证

##### PD-前置条件：短信发送跨越月份边界；统计功能正常；

##### 步骤一：在月末发送各场景短信

##### 步骤二：在月初发送各场景短信

##### 步骤三：查询跨月统计数据

##### 步骤四：验证统计结果准确性

##### ER-预期结果：

###### 1：跨月短信发送成功

###### 2：场景标识准确无误

###### 3：月度统计数据正确

###### 4：跨月数据衔接准确

## 异常测试

### 系统异常处理

#### TL-计费系统异常时场景标识处理验证

##### PD-前置条件：短信服务正常；计费系统可模拟异常；

##### 步骤一：模拟计费系统异常

##### 步骤二：发送各场景短信

##### 步骤三：检查短信发送和场景标识

##### 步骤四：验证异常恢复后的数据补偿

##### ER-预期结果：

###### 1：短信发送不受影响

###### 2：场景标识正常记录

###### 3：计费异常有告警提示

###### 4：异常恢复后数据自动补偿

#### TL-网络中断时数据一致性验证

##### PD-前置条件：网络连接可控制；数据一致性机制正常；

##### 步骤一：发送短信过程中模拟网络中断

##### 步骤二：检查已发送短信的场景标识

##### 步骤三：网络恢复后检查数据状态

##### 步骤四：验证数据一致性

##### ER-预期结果：

###### 1：网络中断前的短信场景标识完整

###### 2：中断期间的数据有保护机制

###### 3：网络恢复后数据自动同步

###### 4：无数据丢失或错乱

#### TL-场景配置错误时系统处理验证

##### PD-前置条件：场景配置可修改；错误处理机制正常；

##### 步骤一：配置错误的场景参数

##### 步骤二：触发短信发送

##### 步骤三：检查系统错误处理

##### 步骤四：验证降级处理结果

##### ER-预期结果：

###### 1：系统检测到配置错误

###### 2：自动启用降级处理机制

###### 3：短信发送不受严重影响

###### 4：错误配置有告警提示

## 性能测试

### 性能影响评估

#### TL-短信发送性能影响测试

##### PD-前置条件：性能测试环境准备；基准性能数据已获取；

##### 步骤一：测试增加场景标识前的短信发送性能

##### 步骤二：启用场景标识功能

##### 步骤三：测试启用后的短信发送性能

##### 步骤四：对比性能差异

##### ER-预期结果：

###### 1：短信发送响应时间增加<100ms

###### 2：系统吞吐量下降<5%

###### 3：资源消耗增加<10%

###### 4：整体性能影响可接受

#### TL-高并发场景标识准确性测试

##### PD-前置条件：高并发测试环境；压力测试工具准备；

##### 步骤一：模拟高并发短信发送请求

##### 步骤二：系统处理并发请求

##### 步骤三：检查场景标识准确性

##### 步骤四：统计错误率和性能指标

##### ER-预期结果：

###### 1：高并发下系统稳定运行

###### 2：场景标识准确率>99.9%

###### 3：响应时间符合SLA要求

###### 4：无系统崩溃或严重错误

#### TL-大数据量统计查询性能测试

##### PD-前置条件：大量历史短信数据；统计查询功能正常；

##### 步骤一：生成大量不同场景的短信数据

##### 步骤二：执行各种统计查询操作

##### 步骤三：测试查询响应时间

##### 步骤四：验证查询结果准确性

##### ER-预期结果：

###### 1：统计查询响应时间<3秒

###### 2：大数据量查询结果准确

###### 3：系统资源消耗合理

###### 4：支持并发查询操作

## 安全测试

### 权限与数据安全

#### TL-不同角色访问权限验证

##### PD-前置条件：多角色权限配置正确；用户账号准备；

##### 步骤一：使用不同角色账号登录

##### 步骤二：尝试访问扣费明细功能

##### 步骤三：检查访问权限控制

##### 步骤四：验证数据可见性

##### ER-预期结果：

###### 1：管理员可查看所有数据

###### 2：客户只能查看自己的数据

###### 3：运营人员可查看统计数据

###### 4：无权限用户无法访问

#### TL-敏感信息保护验证

##### PD-前置条件：敏感信息脱敏规则配置；数据保护机制正常；

##### 步骤一：查看包含敏感信息的扣费明细

##### 步骤二：检查敏感信息显示方式

##### 步骤三：验证数据脱敏效果

##### 步骤四：测试数据导出功能

##### ER-预期结果：

###### 1：手机号码部分脱敏显示

###### 2：身份证号码脱敏处理

###### 3：敏感业务信息保护

###### 4：导出数据同样脱敏

#### TL-API访问安全性验证

##### PD-前置条件：API接口权限配置；安全认证机制正常；

##### 步骤一：使用有效token访问API

##### 步骤二：使用无效token访问API

##### 步骤三：测试API访问频率限制

##### 步骤四：验证数据传输安全性

##### ER-预期结果：

###### 1：有效token正常访问

###### 2：无效token访问被拒绝

###### 3：频率限制机制有效

###### 4：数据传输加密保护

## 兼容性测试

### 多端兼容性

#### TL-PC端扣费明细显示兼容性验证

##### PD-前置条件：PC端系统正常；主流浏览器环境；

##### 步骤一：在不同浏览器打开扣费明细页面

##### 步骤二：检查场景标识显示效果

##### 步骤三：测试筛选和统计功能

##### 步骤四：验证数据导出功能

##### ER-预期结果：

###### 1：各浏览器显示效果一致

###### 2：场景标识清晰可见

###### 3：功能操作正常

###### 4：数据导出成功

#### TL-H5端场景查询兼容性验证

##### PD-前置条件：H5页面正常；移动端浏览器环境；

##### 步骤一：在移动端浏览器访问H5页面

##### 步骤二：查看扣费明细和场景信息

##### 步骤三：测试触屏操作功能

##### 步骤四：验证响应式布局效果

##### ER-预期结果：

###### 1：H5页面正常加载

###### 2：场景信息显示完整

###### 3：触屏操作流畅

###### 4：响应式布局适配良好

#### TL-移动客户端场景统计兼容性验证

##### PD-前置条件：移动客户端正常；不同操作系统设备；

##### 步骤一：在iOS和Android设备打开客户端

##### 步骤二：查看短信扣费统计功能

##### 步骤三：测试场景筛选功能

##### 步骤四：验证数据同步准确性

##### ER-预期结果：

###### 1：iOS和Android显示一致

###### 2：统计数据准确完整

###### 3：筛选功能正常工作

## 线上验证用例

### PATL-各场景短信发送完整流程验证

#### PD-前置条件：生产环境正常；各场景功能可用；

#### 步骤一：依次触发5种场景的短信发送

#### 步骤二：检查每种场景的标识准确性

#### 步骤三：验证扣费记录完整性

#### 步骤四：查看客户账单显示效果

#### ER-预期结果：

##### 1：所有场景短信发送成功

##### 2：场景标识100%准确

##### 3：扣费记录完整无遗漏

##### 4：客户账单显示清晰

### PATL-高峰期场景标识稳定性验证

#### PD-前置条件：业务高峰期；系统负载正常；

#### 步骤一：在高峰期发送各场景短信

#### 步骤二：监控场景标识准确性

#### 步骤三：检查系统性能指标

#### 步骤四：验证扣费数据准确性

#### ER-预期结果：

##### 1：高峰期短信发送正常

##### 2：场景标识准确率>99%

##### 3：系统性能稳定

##### 4：扣费数据准确无误

### PATL-客户对账功能完整验证

#### PD-前置条件：客户账号正常；对账功能可用；

#### 步骤一：客户登录查看扣费明细

#### 步骤二：按场景筛选扣费记录

#### 步骤三：导出对账数据

#### 步骤四：验证数据准确性

#### ER-预期结果：

##### 1：扣费明细显示完整

##### 2：场景筛选功能正常

##### 3：数据导出成功

##### 4：对账数据准确无误

### PATL-多端场景查询一致性验证

#### PD-前置条件：PC、H5、移动端正常；数据同步正常；

#### 步骤一：在PC端查看场景统计数据

#### 步骤二：在H5端查看相同数据

#### 步骤三：在移动端查看相同数据

#### 步骤四：对比三端数据一致性

#### ER-预期结果：

##### 1：三端数据显示一致

##### 2：场景统计数据准确

##### 3：实时同步正常

##### 4：用户体验良好

### PATL-异常恢复后数据完整性验证

#### PD-前置条件：系统异常恢复后；数据恢复机制正常；

#### 步骤一：检查异常期间的短信记录

#### 步骤二：验证场景标识完整性

#### 步骤三：检查扣费数据准确性

#### 步骤四：测试统计功能正常性

#### ER-预期结果：

##### 1：异常期间数据无丢失

##### 2：场景标识完整准确

##### 3：扣费数据自动补偿

##### 4：统计功能正常工作

###### 4：数据实时同步

## 数据兼容性测试

### 历史数据处理

#### TL-历史数据兼容性处理验证

##### PD-前置条件：存在历史短信数据；数据迁移功能正常；

##### 步骤一：查询历史短信数据

##### 步骤二：检查历史数据场景标识处理

##### 步骤三：验证历史数据统计准确性

##### 步骤四：测试历史数据查询功能

##### ER-预期结果：

###### 1：历史数据正常显示

###### 2：未标识场景自动归类为"其他"

###### 3：历史数据统计准确

###### 4：查询功能正常工作

#### TL-数据迁移过程场景标识验证

##### PD-前置条件：数据迁移工具准备；迁移规则配置正确；

##### 步骤一：启动数据迁移过程

##### 步骤二：监控迁移过程中的场景标识处理

##### 步骤三：验证迁移完成后的数据准确性

##### 步骤四：测试迁移后的功能正常性

##### ER-预期结果：

###### 1：数据迁移成功完成

###### 2：场景标识规则正确应用

###### 3：迁移后数据完整准确

###### 4：所有功能正常工作

## 冒烟测试用例

### MYTL-签署通知短信场景标识基本验证

#### PD-前置条件：签署流程已创建；短信服务正常；

#### 步骤一：触发签署通知短信发送

#### 步骤二：检查短信场景标识

#### 步骤三：查看扣费记录

#### ER-预期结果：

##### 1：短信发送成功

##### 2：场景标识为"签署通知"

##### 3：扣费记录正确

### MYTL-实名认证短信场景标识基本验证

#### PD-前置条件：实名认证流程已启动；短信功能正常；

#### 步骤一：申请实名认证短信验证码

#### 步骤二：检查短信场景标识

#### 步骤三：验证扣费记录

#### ER-预期结果：

##### 1：验证码短信发送成功

##### 2：场景标识为"实名认证"

##### 3：扣费记录包含场景信息

### MYTL-核身认证短信场景标识基本验证

#### PD-前置条件：核身认证流程已启动；短信服务配置正确；

#### 步骤一：触发核身认证短信发送

#### 步骤二：检查场景标识

#### 步骤三：查看扣费明细

#### ER-预期结果：

##### 1：核身认证短信发送成功

##### 2：场景标识为"核身认证"

##### 3：扣费明细显示正确

### MYTL-意愿认证短信场景标识基本验证

#### PD-前置条件：意愿认证流程已启动；短信模板配置正确；

#### 步骤一：触发意愿认证短信发送

#### 步骤二：检查场景标识

#### 步骤三：验证扣费记录

#### ER-预期结果：

##### 1：意愿认证短信发送成功

##### 2：场景标识为"意愿认证"

##### 3：扣费记录正确生成

### MYTL-未知场景自动归类基本验证

#### PD-前置条件：存在未预定义场景；自动归类功能正常；

#### 步骤一：触发未知场景短信发送

#### 步骤二：检查系统自动归类处理

#### 步骤三：验证扣费记录

#### ER-预期结果：

##### 1：未知场景短信发送成功

##### 2：自动标识为"其他"场景

##### 3：扣费记录正确分类

### MYTL-扣费明细页面场景显示基本验证

#### PD-前置条件：已有各场景短信扣费记录；页面功能正常；

#### 步骤一：打开扣费明细页面

#### 步骤二：查看场景标识显示

#### 步骤三：测试场景筛选功能

#### ER-预期结果：

##### 1：扣费明细页面正常加载

##### 2：场景标识清晰显示

##### 3：筛选功能正常工作