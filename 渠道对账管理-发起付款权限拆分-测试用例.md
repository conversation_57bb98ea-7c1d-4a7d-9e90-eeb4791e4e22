# 渠道对账管理-发起付款权限拆分及发票核验权限管控-测试用例

## 功能测试

### 权限拆分功能

#### TL-权限拆分后发起付款权限独立性验证

##### PD-前置条件：系统管理员已登录；权限管理模块正常；原发起付款权限已存在；

##### 步骤一：进入权限管理页面，查看原"发起付款"权限配置

##### 步骤二：执行权限拆分操作，将原权限拆分为"发起付款"和"发票核验"两个独立权限

##### 步骤三：检查权限列表，验证拆分结果

##### ER-预期结果：1：权限拆分成功；2：权限列表中显示"发起付款"和"发票核验"两个独立权限；3：两个权限可独立配置和分配；4：原权限配置数据正确迁移；

#### TL-权限拆分后发票核验权限独立性验证

##### PD-前置条件：系统管理员已登录；权限拆分已完成；权限管理模块正常；

##### 步骤一：进入权限管理页面，选择"发票核验"权限

##### 步骤二：查看权限详细配置信息

##### 步骤三：验证权限功能描述和控制范围

##### ER-预期结果：1：发票核验权限独立存在；2：权限描述为"控制核验按钮是否展示"；3：权限功能范围仅包含发票核验相关操作；4：与发起付款权限完全独立；

### 按钮展示控制

#### TL-销售人员登录后发起付款按钮展示验证

##### PD-前置条件：销售人员账号已配置发起付款权限；销售人员账号未配置发票核验权限；渠道对账数据存在；

##### 步骤一：使用销售人员账号登录系统

##### 步骤二：进入渠道对账管理页面

##### 步骤三：查看页面按钮展示情况

##### ER-预期结果：1：页面正常加载；2：发起付款按钮正常展示；3：核验按钮不展示；4：其他功能按钮正常展示；

#### TL-销售人员登录后核验按钮隐藏验证

##### PD-前置条件：销售人员账号已配置发起付款权限；销售人员账号未配置发票核验权限；存在待核验发票数据；

##### 步骤一：使用销售人员账号登录系统

##### 步骤二：进入渠道对账管理页面，选择包含发票的对账记录

##### 步骤三：查看发票操作区域按钮展示

##### ER-预期结果：1：发票信息正常展示；2：核验按钮完全隐藏；3：发票查看功能正常；4：无任何核验相关操作入口；

#### TL-会计人员登录后核验按钮展示验证

##### PD-前置条件：会计人员账号已配置发票核验权限；存在待核验发票数据；渠道对账模块正常；

##### 步骤一：使用会计人员账号登录系统

##### 步骤二：进入渠道对账管理页面

##### 步骤三：选择包含待核验发票的对账记录，查看按钮展示

##### ER-预期结果：1：页面正常加载；2：核验按钮正常展示；3：核验按钮状态为可点击；4：按钮样式符合设计规范；

#### TL-会计人员无发起付款权限时按钮隐藏验证

##### PD-前置条件：会计人员账号仅配置发票核验权限；会计人员账号未配置发起付款权限；对账数据存在；

##### 步骤一：使用会计人员账号登录系统

##### 步骤二：进入渠道对账管理页面

##### 步骤三：查看页面功能按钮展示情况

##### ER-预期结果：1：核验按钮正常展示；2：发起付款按钮不展示；3：其他查看类功能正常；4：页面布局正常；

### 权限分配验证

#### TL-系统管理员为销售人员分配发起付款权限验证

##### PD-前置条件：系统管理员已登录；销售人员账号存在；权限拆分已完成；

##### 步骤一：进入用户权限管理页面，选择销售人员账号

##### 步骤二：为该账号分配"发起付款"权限

##### 步骤三：保存权限配置，验证分配结果

##### ER-预期结果：1：权限分配成功；2：销售人员权限列表包含发起付款权限；3：权限配置立即生效；4：操作日志正确记录；

#### TL-系统管理员为会计人员分配发票核验权限验证

##### PD-前置条件：系统管理员已登录；会计人员账号存在；权限拆分已完成；

##### 步骤一：进入用户权限管理页面，选择会计人员账号

##### 步骤二：为该账号分配"发票核验"权限

##### 步骤三：保存权限配置，验证分配结果

##### ER-预期结果：1：权限分配成功；2：会计人员权限列表包含发票核验权限；3：权限配置立即生效；4：操作日志正确记录；

#### TL-批量权限分配验证

##### PD-前置条件：系统管理员已登录；存在多个销售人员和会计人员账号；权限拆分已完成；

##### 步骤一：进入批量权限管理页面

##### 步骤二：选择所有销售人员，批量分配发起付款权限

##### 步骤三：选择所有会计人员，批量分配发票核验权限

##### 步骤四：执行批量分配操作

##### ER-预期结果：1：批量分配操作成功；2：所有销售人员获得发起付款权限；3：所有会计人员获得发票核验权限；4：分配结果准确无误；

### 业务流程验证

#### TL-销售人员发起付款完整流程验证

##### PD-前置条件：销售人员已配置发起付款权限；存在可发起付款的对账数据；付款流程正常；

##### 步骤一：销售人员登录系统，进入渠道对账管理页面

##### 步骤二：选择对账记录，点击发起付款按钮

##### 步骤三：填写付款信息，提交付款申请

##### 步骤四：验证付款流程状态变更

##### ER-预期结果：1：发起付款按钮正常展示和点击；2：付款信息填写页面正常；3：付款申请提交成功；4：对账记录状态更新为"付款中"；

#### TL-会计人员发票核验完整流程验证

##### PD-前置条件：会计人员已配置发票核验权限；存在待核验发票；核验流程正常；

##### 步骤一：会计人员登录系统，进入渠道对账管理页面

##### 步骤二：选择包含待核验发票的对账记录

##### 步骤三：点击核验按钮，进行发票核验操作

##### 步骤四：完成核验，提交核验结果

##### ER-预期结果：1：核验按钮正常展示和点击；2：发票核验页面正常加载；3：核验操作执行成功；4：发票状态更新为"已核验"；

#### TL-销售上传发票后无法自行核验验证

##### PD-前置条件：销售人员已配置发起付款权限但无发票核验权限；对账记录存在；

##### 步骤一：销售人员登录系统，进入渠道对账管理页面

##### 步骤二：选择对账记录，上传渠道费发票

##### 步骤三：发票上传成功后，查看可执行操作

##### ER-预期结果：1：发票上传成功；2：发票信息正常展示；3：核验按钮不展示；4：销售人员无法执行核验操作；

## 异常测试

### 权限异常场景

#### TL-无权限用户访问发起付款功能验证

##### PD-前置条件：用户账号未配置发起付款权限；用户已登录系统；对账数据存在；

##### 步骤一：用户进入渠道对账管理页面

##### 步骤二：尝试查找发起付款相关功能入口

##### 步骤三：通过URL直接访问发起付款页面

##### ER-预期结果：1：发起付款按钮不展示；2：直接访问返回权限不足提示；3：无法执行发起付款操作；4：系统记录非法访问日志；

#### TL-无权限用户访问发票核验功能验证

##### PD-前置条件：用户账号未配置发票核验权限；用户已登录系统；存在待核验发票；

##### 步骤一：用户进入渠道对账管理页面

##### 步骤二：尝试查找发票核验相关功能入口

##### 步骤三：通过URL直接访问核验页面

##### ER-预期结果：1：核验按钮不展示；2：直接访问返回权限不足提示；3：无法执行核验操作；4：系统记录非法访问日志；

### 系统异常场景

#### TL-权限服务异常时按钮展示验证

##### PD-前置条件：用户已登录；权限服务出现异常；渠道对账页面可访问；

##### 步骤一：模拟权限服务异常

##### 步骤二：用户进入渠道对账管理页面

##### 步骤三：查看页面按钮展示情况

##### ER-预期结果：1：页面基本功能正常；2：敏感操作按钮默认隐藏；3：显示权限验证失败提示；4：用户无法执行需要权限的操作；

## 兼容性测试

### 浏览器兼容性

#### TL-Chrome浏览器权限控制功能验证

##### PD-前置条件：Chrome浏览器环境；用户账号权限已配置；系统功能正常；

##### 步骤一：使用Chrome浏览器登录系统

##### 步骤二：进入渠道对账管理页面

##### 步骤三：验证按钮展示和权限控制功能

##### ER-预期结果：1：页面正常加载；2：按钮展示符合权限配置；3：权限控制功能正常；4：交互体验良好；

#### TL-Firefox浏览器权限控制功能验证

##### PD-前置条件：Firefox浏览器环境；用户账号权限已配置；系统功能正常；

##### 步骤一：使用Firefox浏览器登录系统

##### 步骤二：进入渠道对账管理页面

##### 步骤三：验证按钮展示和权限控制功能

##### ER-预期结果：1：页面正常加载；2：按钮展示符合权限配置；3：权限控制功能正常；4：与Chrome表现一致；

## 性能测试

### 权限验证性能

#### TL-大量用户同时访问权限验证性能

##### PD-前置条件：系统部署完成；权限配置正确；性能测试环境准备；

##### 步骤一：模拟100个用户同时登录系统

##### 步骤二：所有用户同时访问渠道对账管理页面

##### 步骤三：监控权限验证响应时间和系统资源使用

##### ER-预期结果：1：权限验证响应时间小于2秒；2：页面加载时间小于3秒；3：系统资源使用正常；4：无权限验证错误；

## 安全测试

### 权限绕过测试

#### TL-尝试绕过权限控制执行核验操作

##### PD-前置条件：销售人员账号无核验权限；了解核验接口地址；存在待核验发票；

##### 步骤一：销售人员登录系统

##### 步骤二：通过开发者工具直接调用核验接口

##### 步骤三：尝试修改前端代码显示核验按钮

##### ER-预期结果：1：直接调用接口返回权限不足；2：前端修改无法绕过后端权限验证；3：所有绕过尝试均失败；4：系统记录安全日志；

### 详细功能测试场景

#### TL-权限拆分前后数据一致性验证

##### PD-前置条件：系统存在历史权限配置数据；权限拆分脚本准备完成；数据库备份完成；

##### 步骤一：记录拆分前所有用户的权限配置情况

##### 步骤二：执行权限拆分操作

##### 步骤三：验证拆分后用户权限配置的正确性

##### 步骤四：对比拆分前后的权限覆盖范围

##### ER-预期结果：1：原有发起付款权限用户自动获得新的发起付款权限；2：原有发起付款权限用户不自动获得发票核验权限；3：权限拆分不影响其他权限配置；4：数据迁移完整无丢失；

#### TL-多角色用户权限组合验证

##### PD-前置条件：用户同时拥有销售和会计角色；权限配置支持多角色；系统正常运行；

##### 步骤一：为用户同时分配销售角色的发起付款权限和会计角色的发票核验权限

##### 步骤二：用户登录系统，进入渠道对账管理页面

##### 步骤三：验证用户可执行的操作范围

##### ER-预期结果：1：发起付款按钮正常展示；2：核验按钮正常展示；3：用户可同时执行两种操作；4：权限控制逻辑正确；

#### TL-权限动态变更实时生效验证

##### PD-前置条件：用户已登录系统；系统管理员可修改用户权限；权限变更机制正常；

##### 步骤一：用户登录并保持在渠道对账管理页面

##### 步骤二：系统管理员移除用户的发票核验权限

##### 步骤三：用户刷新页面或执行相关操作

##### ER-预期结果：1：权限变更立即生效；2：核验按钮从页面消失；3：用户无法继续执行核验操作；4：系统提示权限已变更；

#### TL-权限继承关系验证

##### PD-前置条件：系统支持角色权限继承；存在上下级角色关系；权限配置正确；

##### 步骤一：为上级角色配置发起付款和发票核验权限

##### 步骤二：为下级角色仅配置发起付款权限

##### 步骤三：验证不同级别用户的权限表现

##### ER-预期结果：1：上级角色用户拥有两个权限；2：下级角色用户仅拥有发起付款权限；3：权限继承关系正确；4：不存在权限越级现象；

#### TL-权限撤销后历史数据访问验证

##### PD-前置条件：用户之前拥有发票核验权限并执行过核验操作；现已撤销核验权限；历史核验记录存在；

##### 步骤一：撤销用户的发票核验权限

##### 步骤二：用户登录系统，查看历史核验记录

##### 步骤三：尝试访问之前核验的发票详情

##### ER-预期结果：1：历史核验记录可正常查看；2：发票详情可正常访问；3：无法执行新的核验操作；4：历史操作记录完整保留；

#### TL-并发权限操作冲突处理验证

##### PD-前置条件：多个管理员同时在线；同一用户权限可被多人修改；系统支持并发控制；

##### 步骤一：管理员A开始修改用户权限配置

##### 步骤二：管理员B同时修改同一用户的权限配置

##### 步骤三：两个管理员几乎同时提交权限变更

##### ER-预期结果：1：系统正确处理并发冲突；2：后提交的变更覆盖先提交的变更或提示冲突；3：最终权限状态一致；4：操作日志完整记录；

#### TL-权限配置错误恢复验证

##### PD-前置条件：系统支持权限配置回滚；存在权限配置备份；管理员权限充足；

##### 步骤一：记录当前正确的权限配置状态

##### 步骤二：故意配置错误的权限（如给销售分配核验权限）

##### 步骤三：发现错误后执行权限配置回滚

##### ER-预期结果：1：错误配置可被及时发现；2：回滚操作执行成功；3：权限配置恢复到正确状态；4：受影响用户权限正确恢复；

#### TL-大批量用户权限批量操作验证

##### PD-前置条件：系统存在1000+用户账号；支持批量权限操作；系统性能正常；

##### 步骤一：选择所有销售人员（假设500人），批量分配发起付款权限

##### 步骤二：选择所有会计人员（假设200人），批量分配发票核验权限

##### 步骤三：监控批量操作执行过程和结果

##### ER-预期结果：1：批量操作在合理时间内完成；2：所有用户权限分配正确；3：系统性能稳定；4：操作日志完整记录；

#### TL-权限缓存一致性验证

##### PD-前置条件：系统使用权限缓存机制；缓存更新策略正常；分布式环境部署；

##### 步骤一：用户在节点A登录并获得权限缓存

##### 步骤二：管理员在节点B修改用户权限

##### 步骤三：用户在节点A执行需要权限的操作

##### ER-预期结果：1：权限变更在所有节点同步；2：缓存更新及时有效；3：用户权限验证结果一致；4：不存在缓存不一致问题；

#### TL-权限审计日志完整性验证

##### PD-前置条件：系统启用权限审计功能；日志记录机制正常；存储空间充足；

##### 步骤一：执行各种权限相关操作（分配、撤销、修改）

##### 步骤二：用户执行各种业务操作（发起付款、核验发票）

##### 步骤三：查看权限审计日志记录

##### ER-预期结果：1：所有权限操作均有日志记录；2：日志包含操作人、时间、操作内容等关键信息；3：日志格式规范统一；4：日志数据完整不丢失；

## 冒烟测试用例

### MYTL-销售人员发起付款基本功能验证

#### PD-前置条件：销售人员已配置发起付款权限；对账数据存在；

#### 步骤一：销售人员登录系统，进入渠道对账管理页面

#### 步骤二：点击发起付款按钮，填写基本付款信息

#### 步骤三：提交付款申请

#### ER-预期结果：1：发起付款按钮正常展示；2：付款申请提交成功；3：对账状态正确更新；

### MYTL-会计人员发票核验基本功能验证

#### PD-前置条件：会计人员已配置发票核验权限；待核验发票存在；

#### 步骤一：会计人员登录系统，进入渠道对账管理页面

#### 步骤二：点击核验按钮，执行发票核验

#### 步骤三：完成核验操作

#### ER-预期结果：1：核验按钮正常展示；2：核验操作执行成功；3：发票状态正确更新；

### MYTL-销售人员无核验权限验证

#### PD-前置条件：销售人员仅有发起付款权限；待核验发票存在；

#### 步骤一：销售人员登录系统，进入渠道对账管理页面

#### 步骤二：查看发票相关操作按钮

#### ER-预期结果：1：核验按钮不展示；2：销售人员无法执行核验操作；

### MYTL-权限拆分后系统基本功能验证

#### PD-前置条件：权限拆分已完成；用户权限已重新分配；

#### 步骤一：验证权限管理页面显示两个独立权限

#### 步骤二：验证用户权限分配正确

#### ER-预期结果：1：权限拆分成功；2：用户权限分配正确；3：系统功能正常；

## 线上验证用例

### PATL-生产环境权限拆分验证

#### PD-前置条件：生产环境权限拆分已部署；用户权限已重新配置；

#### 步骤一：验证权限拆分在生产环境正确执行

#### 步骤二：抽样验证不同角色用户的权限表现

#### ER-预期结果：1：权限拆分部署成功；2：用户权限控制正确；3：业务功能正常；

### PATL-关键业务流程验证

#### PD-前置条件：生产环境系统正常；真实业务数据存在；

#### 步骤一：销售人员执行发起付款流程

#### 步骤二：会计人员执行发票核验流程

#### ER-预期结果：1：发起付款流程正常；2：发票核验流程正常；3：业务闭环完整；

### PATL-权限安全性验证

#### PD-前置条件：生产环境部署完成；安全策略生效；

#### 步骤一：验证销售人员无法执行核验操作

#### 步骤二：验证权限控制机制有效性

#### ER-预期结果：1：权限控制严格有效；2：无权限绕过风险；3：安全策略正确执行；
