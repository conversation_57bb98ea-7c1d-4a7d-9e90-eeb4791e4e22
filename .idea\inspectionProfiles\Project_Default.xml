<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyCompatibilityInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ourVersions">
        <value>
          <list size="2">
            <item index="0" class="java.lang.String" itemvalue="2.7" />
            <item index="1" class="java.lang.String" itemvalue="3.11" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="18">
            <item index="0" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="1" class="java.lang.String" itemvalue="pydantic" />
            <item index="2" class="java.lang.String" itemvalue="rocketmq" />
            <item index="3" class="java.lang.String" itemvalue="PyYAML" />
            <item index="4" class="java.lang.String" itemvalue="demjson" />
            <item index="5" class="java.lang.String" itemvalue="crypto" />
            <item index="6" class="java.lang.String" itemvalue="jmespath" />
            <item index="7" class="java.lang.String" itemvalue="eventlet" />
            <item index="8" class="java.lang.String" itemvalue="selenium" />
            <item index="9" class="java.lang.String" itemvalue="pymysql" />
            <item index="10" class="java.lang.String" itemvalue="Faker" />
            <item index="11" class="java.lang.String" itemvalue="pypinyin" />
            <item index="12" class="java.lang.String" itemvalue="httprunner" />
            <item index="13" class="java.lang.String" itemvalue="openpyxl" />
            <item index="14" class="java.lang.String" itemvalue="numpy" />
            <item index="15" class="java.lang.String" itemvalue="uwsgi" />
            <item index="16" class="java.lang.String" itemvalue="requests" />
            <item index="17" class="java.lang.String" itemvalue="Flask" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
          <option value="N803" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="Utils.zhuqi.billingLock.billingLockCases.*" />
          <option value="billing.Utils.zhuqi.model.baseBillingModel.*" />
          <option value="utils.db.curd.baseBilling.serviceLog" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>